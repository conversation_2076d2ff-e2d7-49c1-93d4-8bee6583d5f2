'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RiSearchLine, RiFilterLine, RiUserLine } from '@remixicon/react';
import { useCustomersList } from '@/hooks/use-customers';
import { DraggableCustomerItem } from './draggable-customer-item';
import type { CustomerResponse } from '@/lib/validations';
import { Sidebar, SidebarContent, SidebarHeader, SidebarTrigger } from '../ui/sidebar';

interface CustomerSidebarProps {
  className?: string;
}

export function CustomerSidebar({ className }: CustomerSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlyWithSessions, setShowOnlyWithSessions] = useState(false);

  // Fetch customers with search
  const { customers, loading, error } = useCustomersList({
    search: searchQuery,
    limit: 100, // Get more customers for the sidebar
  });

  // Filter customers based on sessions filter
  const filteredCustomers = useMemo(() => {
    if (!showOnlyWithSessions) return customers;
    return customers.filter((customer) => (customer.totalSessions || 0) > 0);
  }, [customers, showOnlyWithSessions]);

  // Group customers by session status
  const { withSessions, withoutSessions } = useMemo(() => {
    const withSessions: CustomerResponse[] = [];
    const withoutSessions: CustomerResponse[] = [];

    filteredCustomers.forEach((customer) => {
      if ((customer.totalSessions || 0) > 0) {
        withSessions.push(customer);
      } else {
        withoutSessions.push(customer);
      }
    });

    return { withSessions, withoutSessions };
  }, [filteredCustomers]);

  return (
    <Sidebar variant="inset" side="right" className="dark scheme-only-dark max-lg:p-3 lg:ps-1">
      <SidebarHeader>
        <div className="flex justify-between items-center gap-2">
          <SidebarTrigger
            direction="right"
            className="text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent!"
          />
          <div className="flex items-center gap-2">
            <RiUserLine className="h-5 w-5" />
            Customers
          </div>
        </div>
        <span className="text-sm text-muted-foreground">Drag customers to schedule them for workouts</span>
      </SidebarHeader>
      <SidebarContent className="mt-3 pt-3 border-t px-1 -mx-1">
        <div className="relative flex-shrink-0">
          <RiSearchLine className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search customers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex items-center gap-2 flex-shrink-0">
          <Button
            variant={showOnlyWithSessions ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowOnlyWithSessions(!showOnlyWithSessions)}
            className="flex items-center gap-1"
          >
            <RiFilterLine className="h-3 w-3" />
            Sessions only
          </Button>
          <Badge variant="secondary" className="text-xs">
            {filteredCustomers.length} customers
          </Badge>
        </div>

        <div className="flex-1 min-h-0">
          {error ? (
            <div className="text-center text-muted-foreground py-8">
              <p>Error loading customers</p>
              <p className="text-sm">{error}</p>
            </div>
          ) : (
            <ScrollArea className="h-full -mx-6 px-6 pb-2">
              <div className="space-y-2">
                {loading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, i) => (
                    <Card key={i}>
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 flex-1">
                            <Skeleton className="h-4 w-4 rounded-full" />
                            <div className="space-y-1 flex-1">
                              <Skeleton className="h-4 w-24" />
                              <Skeleton className="h-3 w-32" />
                            </div>
                          </div>
                          <Skeleton className="h-5 w-8" />
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <>
                    {withSessions.length > 0 && (
                      <>
                        {withSessions.map((customer) => (
                          <DraggableCustomerItem key={customer.id} customer={customer} />
                        ))}
                      </>
                    )}

                    {withoutSessions.length > 0 && !showOnlyWithSessions && (
                      <>
                        {withSessions.length > 0 && (
                          <div className="py-2">
                            <div className="text-xs text-muted-foreground font-medium">No Sessions</div>
                          </div>
                        )}
                        {withoutSessions.map((customer) => (
                          <DraggableCustomerItem key={customer.id} customer={customer} canDrag={false} />
                        ))}
                      </>
                    )}

                    {filteredCustomers.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <RiUserLine className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">{searchQuery ? 'No customers found' : 'No customers available'}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </ScrollArea>
          )}
        </div>
      </SidebarContent>
    </Sidebar>
  );
}
