'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import useSWR from 'swr';
import type { CustomerPackageResponse, PaginatedResponse } from '@/lib/validations';

interface CustomerPackagesListProps {
  customerId: string;
}

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch');
  }
  return response.json();
};

export function CustomerPackagesList({ customerId }: CustomerPackagesListProps) {
  const {
    data: packagesResponse,
    error,
    isLoading,
  } = useSWR<PaginatedResponse<CustomerPackageResponse>>(`/api/customers/${customerId}/packages`, fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 5000,
  });

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Failed to load customer packages.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-2 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const packages = packagesResponse?.data || [];
  const totalSessions = packages.reduce((sum, pkg) => sum + pkg.sessionsRemaining, 0);

  if (packages.length === 0) {
    return (
      <Card>
        <CardContent>
          <div className="text-center text-muted-foreground py-4">
            <p>This customer has no session packages.</p>
            <p className="text-sm">Assign a package to get started.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Session Summary</CardTitle>
          <CardDescription>Total sessions available across all packages</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total Sessions Remaining:</span>
            <Badge variant={totalSessions > 0 ? 'default' : 'secondary'} className="text-base px-3 py-1">
              {totalSessions}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-3">
        <h3 className="text-sm font-medium text-muted-foreground">Package Breakdown</h3>
        {packages.map((package_) => {
          const usedSessions = package_.originalSessionCount - package_.sessionsRemaining;
          const progressPercentage = (usedSessions / package_.originalSessionCount) * 100;

          return (
            <Card key={package_.id}>
              <CardContent className="pt-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{package_.packageName}</h4>
                      {package_.packageDescription && (
                        <p className="text-sm text-muted-foreground">{package_.packageDescription}</p>
                      )}
                    </div>
                    <Badge variant={package_.sessionsRemaining > 0 ? 'default' : 'secondary'}>
                      {package_.sessionsRemaining} / {package_.originalSessionCount}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="text-muted-foreground">
                        {usedSessions} used, {package_.sessionsRemaining} remaining
                      </span>
                    </div>
                    <Progress value={progressPercentage} className="h-2" />
                  </div>

                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>Purchased: {new Date(package_.purchaseDate).toLocaleDateString()}</span>
                    {package_.sessionsRemaining === 0 && (
                      <Badge variant="outline" className="text-xs">
                        Completed
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
