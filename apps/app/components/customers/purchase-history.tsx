'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import useSWR from 'swr';
import { fetcher } from '@/lib/fetcher';
import type { CustomerPackageResponse, PaginatedResponse } from '@/lib/validations';

interface PurchaseHistoryProps {
  customerId: string;
}

export function PurchaseHistory({ customerId }: PurchaseHistoryProps) {
  const {
    data: packagesResponse,
    error,
    isLoading,
    mutate,
  } = useSWR<PaginatedResponse<CustomerPackageResponse>>(
    customerId ? `/api/customers/${customerId}/packages?includeExpired=true` : null,
    fetcher
  );

  const packages = packagesResponse?.data || [];

  const loading = isLoading;

  if (loading) {
    return <PurchaseHistorySkeleton />;
  }

  if (error) {
    return (
      <div className="text-center text-muted-foreground">
        <p>Error loading package history: {error.message || error}</p>
        <Button variant="outline" onClick={() => mutate()} className="mt-2">
          Try Again
        </Button>
      </div>
    );
  }

  if (!packages || packages.length === 0) {
    return (
      <div className="text-center text-muted-foreground">
        <p>No package history found.</p>
        <p className="text-sm">Assign the first package to get started.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground">
        {packages.length} package{packages.length !== 1 ? 's' : ''} found
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {packages.map((pkg) => (
          <Card key={pkg.id}>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {pkg.sessionsRemaining} / {pkg.originalSessionCount} sessions
                    </span>
                    <Badge variant="outline" className="text-xs">
                      Package
                    </Badge>
                  </div>
                  <div className="text-sm font-medium text-primary">{pkg.packageName}</div>
                  {pkg.packageDescription && (
                    <div className="text-xs text-muted-foreground">{pkg.packageDescription}</div>
                  )}
                  <div className="text-sm text-muted-foreground">
                    Assigned: {new Date(pkg.purchaseDate).toLocaleDateString()} at{' '}
                    {new Date(pkg.purchaseDate).toLocaleTimeString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Package Assignment</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

function PurchaseHistorySkeleton() {
  return (
    <div className="space-y-4 pt-8">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-16" />
            </div>
            <Skeleton className="h-3 w-32" />
          </div>
          <div className="text-right space-y-2">
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      ))}
    </div>
  );
}
