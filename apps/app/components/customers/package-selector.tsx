'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { RiCheckLine } from '@remixicon/react';
import { useActivePackages } from '@/hooks/use-packages';
import type { PackageResponse } from '@/lib/validations';

interface PackageSelectorProps {
  selectedPackageId?: string;
  onPackageSelect: (package_: PackageResponse) => void;
  disabled?: boolean;
}

export function PackageSelector({ selectedPackageId, onPackageSelect, disabled }: PackageSelectorProps) {
  const { data: packagesResponse, error, isLoading } = useActivePackages();

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Failed to load packages. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-3 w-1/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const packages = packagesResponse?.data || [];

  if (packages.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>No packages available.</p>
            <p className="text-sm">Contact your administrator to create packages.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium">Select a Package</h3>
        <p className="text-sm text-muted-foreground">Choose a session package to assign to the customer</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {packages.map((package_) => {
          const isSelected = selectedPackageId === package_.id;
          const pricePerSession = parseFloat(package_.price) / package_.sessionCount;

          return (
            <Card
              key={package_.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary' : ''
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => !disabled && onPackageSelect(package_)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{package_.name}</CardTitle>
                  {isSelected && (
                    <div className="flex items-center justify-center w-6 h-6 bg-primary text-primary-foreground rounded-full">
                      <RiCheckLine className="h-4 w-4" />
                    </div>
                  )}
                </div>
                {package_.description && <CardDescription className="text-sm">{package_.description}</CardDescription>}
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Sessions:</span>
                    <Badge variant="secondary">{package_.sessionCount}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Total Price:</span>
                    <span className="font-medium">RM {parseFloat(package_.price).toFixed(2)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Per Session:</span>
                    <span className="text-sm">RM {pricePerSession.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
