'use client';

import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { PackageResponse } from '@/lib/validations';

interface DeletePackageDialogProps {
  package_: PackageResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export function DeletePackageDialog({ package_, open, onOpenChange, onConfirm }: DeletePackageDialogProps) {
  if (!package_) return null;

  const pricePerSession = parseFloat(package_.price) / package_.sessionCount;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Package</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this package? This action will deactivate the package.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 border rounded-lg bg-muted/50">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">{package_.name}</h3>
              <Badge variant={package_.isActive ? 'default' : 'secondary'}>
                {package_.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            {package_.description && (
              <p className="text-sm text-muted-foreground mb-2">{package_.description}</p>
            )}
            <div className="text-sm text-muted-foreground space-y-1">
              <div>Sessions: {package_.sessionCount}</div>
              <div>Price: RM {parseFloat(package_.price).toFixed(2)}</div>
              <div>Per Session: RM {pricePerSession.toFixed(2)}</div>
            </div>
          </div>

          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Important:</strong> This will deactivate the package and prevent it from being assigned to new customers. 
              Existing customer assignments will remain valid.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={onConfirm}>
            Delete Package
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
