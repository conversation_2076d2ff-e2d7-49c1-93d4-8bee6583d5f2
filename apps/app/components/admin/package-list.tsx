'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import useSWRInfinite from 'swr/infinite';
import { toast } from 'sonner';
import { getPackagesKey, deletePackage, fetcher } from '@/hooks/use-packages';
import { RiEditLine, RiDeleteBinLine, RiEyeLine, RiEyeOffLine } from '@remixicon/react';
import type { PackageResponse } from '@/lib/validations';
import { EditPackageDialog } from './edit-package-dialog';
import { DeletePackageDialog } from './delete-package-dialog';

interface PackageListProps {
  searchQuery: string;
}

export function PackageList({ searchQuery }: PackageListProps) {
  const [selectedPackage, setSelectedPackage] = useState<PackageResponse | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [showInactive, setShowInactive] = useState(false);

  const { data, error, size, setSize, isLoading, isValidating, mutate } = useSWRInfinite(
    (index) => getPackagesKey(index, 20, searchQuery, showInactive ? undefined : true),
    fetcher, // Pass fetcher here
    { revalidateFirstPage: false }
  );

  const packages = data ? data.flatMap((page) => page?.data) : [];
  const isLoadingMore = isLoading || (size > 0 && data && typeof data[size - 1] === 'undefined');
  const isEmpty = data?.[0]?.data.length === 0;
  const isReachingEnd = isEmpty || (data && data[data.length - 1]?.pagination.hasMore === false);

  const handleEdit = (package_: PackageResponse) => {
    setSelectedPackage(package_);
    setIsEditDialogOpen(true);
  };

  const handleDelete = (package_: PackageResponse) => {
    setSelectedPackage(package_);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedPackage) return;

    try {
      await deletePackage(selectedPackage.id);
      toast.success('Package deleted successfully');
      mutate();
      setIsDeleteDialogOpen(false);
      setSelectedPackage(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete package';
      toast.error(errorMessage);
    }
  };

  const handlePackageChanged = () => {
    mutate();
  };

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Failed to load packages. Please try again.</p>
            <Button variant="outline" onClick={() => mutate()} className="mt-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Packages</CardTitle>
              <CardDescription>
                {packages.length} package{packages.length !== 1 ? 's' : ''} found
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => setShowInactive(!showInactive)}>
              {showInactive ? (
                <>
                  <RiEyeOffLine className="mr-2 h-4 w-4" />
                  Hide Inactive
                </>
              ) : (
                <>
                  <RiEyeLine className="mr-2 h-4 w-4" />
                  Show Inactive
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-3 w-1/2" />
                    <Skeleton className="h-3 w-1/4" />
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              ))}
            </div>
          ) : isEmpty ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No packages found.</p>
              <p className="text-sm">Create your first package to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {packages.map((package_) => (
                <div
                  key={package_.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="space-y-1 flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{package_.name}</h3>
                      <Badge variant={package_.isActive ? 'default' : 'secondary'}>
                        {package_.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    {package_.description && <p className="text-sm text-muted-foreground">{package_.description}</p>}
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>{package_.sessionCount} sessions</span>
                      <span>RM {parseFloat(package_.price).toFixed(2)}</span>
                      <span>RM {(parseFloat(package_.price) / package_.sessionCount).toFixed(2)} per session</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(package_)}>
                      <RiEditLine className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDelete(package_)}>
                      <RiDeleteBinLine className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {!isReachingEnd && (
                <div className="flex justify-center pt-4">
                  <Button variant="outline" onClick={() => setSize(size + 1)} disabled={isLoadingMore}>
                    {isLoadingMore ? 'Loading...' : 'Load More'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <EditPackageDialog
        package_={selectedPackage}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onPackageChanged={handlePackageChanged}
      />

      <DeletePackageDialog
        package_={selectedPackage}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
      />
    </>
  );
}
