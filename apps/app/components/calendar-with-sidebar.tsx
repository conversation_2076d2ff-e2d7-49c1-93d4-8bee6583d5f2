'use client';

import { useState, useMemo } from 'react';
import { startOfWeek, endOfWeek } from 'date-fns';
import { useCalendarContext } from '@/components/event-calendar/calendar-context';
import { useWorkoutsList, createWorkout, updateWorkout, deleteWorkout, addParticipant } from '@/hooks/use-workouts';
import { CustomerSidebar } from '@/components/customers/customer-sidebar';
import { WorkoutDetailsModal } from '@/components/workouts/workout-details-modal';
import { WorkoutEditModal } from '@/components/workouts/workout-edit-modal';

import {
  EventCalendar,
  type CalendarEvent,
  workoutToCalendarEvent,
  CalendarDndProvider,
} from '@/components/event-calendar';
import type { CustomerResponse } from '@/lib/validations';
import { hasCustomerConflict, hasWorkoutTimeConflict, formatConflictMessage } from '@/lib/workout-utils';
import { toast } from 'sonner';
import { useSidebar } from './ui/sidebar';

// Etiquettes data for calendar filtering
export const etiquettes = [
  {
    id: 'scheduled',
    color: '#3b82f6',
    label: 'Scheduled',
    isActive: true,
  },
  {
    id: 'confirmed',
    color: '#10b981',
    label: 'Confirmed',
    isActive: true,
  },
  {
    id: 'completed',
    color: '#6b7280',
    label: 'Completed',
    isActive: true,
  },
  {
    id: 'cancelled',
    color: '#ef4444',
    label: 'Cancelled',
    isActive: false,
  },
];

export default function CalendarWithSidebar() {
  const { currentDate, isColorVisible } = useCalendarContext();
  const [selectedWorkoutId, setSelectedWorkoutId] = useState<string | null>(null);
  const [workoutModalOpen, setWorkoutModalOpen] = useState(false);
  const [editWorkout, setEditWorkout] = useState<any | null>(null);
  const [workoutEditModalOpen, setWorkoutEditModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Calculate date range for fetching workouts (current week)
  const startDate = startOfWeek(currentDate, { weekStartsOn: 0 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 0 });

  // Fetch workouts for the current date range
  const { workouts, loading, error, mutate } = useWorkoutsList({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    limit: 100, // Maximum allowed by API
  });

  // Convert workouts to calendar events
  const events = useMemo(() => {
    return workouts.map(workoutToCalendarEvent);
  }, [workouts]);

  // Filter events based on visible colors
  const visibleEvents = useMemo(() => {
    return events.filter((event) => isColorVisible(event.color));
  }, [events, isColorVisible]);

  const { leftSidebarOpen, rightSidebarOpen } = useSidebar();

  const handleWorkoutCreate = async (startTime: Date) => {
    try {
      // Create new workout at the clicked time
      const workoutStart = new Date(startTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for workout time conflicts before creating new workout
      const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
        workoutStart,
        workoutEnd,
        workouts
      );

      if (hasTimeConflict && conflictingWorkouts.length > 0) {
        toast.error('Cannot create workout: time slot conflicts with existing workout');
        return;
      }

      const newWorkout = await createWorkout({
        title: `Training Session`,
        description: '',
        startTime: startTime.toISOString(),
        endTime: workoutEnd.toISOString(),
        location: '',
        minParticipants: 3,
        maxParticipants: 5,
      });

      if (newWorkout) {
        toast.success('Workout created successfully');
        mutate(); // Refresh the workout list
      }
    } catch (error) {
      console.error('Error creating workout:', error);
      toast.error('Failed to create workout');
    }
  };

  const handleWorkoutUpdate = async (event: CalendarEvent) => {
    try {
      if (!event.workoutId) return;

      const updatedWorkout = await updateWorkout(event.workoutId, {
        startTime: event.start.toISOString(),
        endTime: event.end.toISOString(),
      });

      if (updatedWorkout) {
        toast.success('Workout updated successfully');
        mutate(); // Refresh the workout list
      }
    } catch (error) {
      console.error('Error updating workout:', error);
      toast.error('Failed to update workout');
    }
  };

  const handleWorkoutDeleteFromCalendar = async (eventId: string) => {
    try {
      await deleteWorkout(eventId);
      toast.success('Workout deleted successfully');
      mutate(); // Refresh the workout list
    } catch (error) {
      console.error('Error deleting workout:', error);
      toast.error('Failed to delete workout');
    }
  };

  const handleWorkoutClick = (event: CalendarEvent) => {
    if (event.workoutId) {
      setSelectedWorkoutId(event.workoutId);
      setWorkoutModalOpen(true);
    }
  };

  const handleWorkoutEdit = (workout: any) => {
    setEditWorkout(workout);
    setWorkoutEditModalOpen(true);
    setWorkoutModalOpen(false);
  };

  const handleWorkoutDelete = async (workoutId: string) => {
    try {
      await deleteWorkout(workoutId);
      toast.success('Workout deleted successfully');
      setWorkoutModalOpen(false);
      mutate(); // Refresh the workout list
    } catch (error) {
      console.error('Error deleting workout:', error);
      toast.error('Failed to delete workout');
    }
  };

  const handleCustomerDrop = async (customer: CustomerResponse, targetDate: Date, targetTime?: Date) => {
    try {
      // Check if customer has sessions
      if ((customer.totalSessions || 0) <= 0) {
        toast.error('Customer has no sessions available');
        return;
      }

      // Calculate target workout time
      const targetDateTime = targetTime || targetDate;
      const workoutStart = new Date(targetDateTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for customer conflicts
      const { hasConflict, conflictingWorkout } = hasCustomerConflict(customer.id, workoutStart, workoutEnd, workouts);

      if (hasConflict && conflictingWorkout) {
        toast.error(formatConflictMessage(customer.name, conflictingWorkout));
        return;
      }

      // Check if there's an existing workout at this time
      const existingWorkout = workouts.find((workout) => {
        const workoutStart = new Date(workout.startTime);
        const workoutEnd = new Date(workout.endTime);
        const targetStart = new Date(targetDateTime);
        const targetEnd = new Date(targetStart);
        targetEnd.setHours(targetStart.getHours() + 1);

        return (
          (targetStart >= workoutStart && targetStart < workoutEnd) ||
          (targetEnd > workoutStart && targetEnd <= workoutEnd) ||
          (targetStart <= workoutStart && targetEnd >= workoutEnd)
        );
      });

      if (existingWorkout) {
        // Check if workout is at capacity
        const currentParticipants = existingWorkout.participantCount || 0;
        const maxParticipants = existingWorkout.maxParticipants || 5;

        if (currentParticipants >= maxParticipants) {
          toast.error('Workout is at maximum capacity');
          return;
        }

        // Add customer to existing workout
        const newParticipant = await addParticipant(existingWorkout.id, { customerId: customer.id });
        if (newParticipant) {
          toast.success(`${customer.name} added to ${existingWorkout.title}`);
        }
      } else {
        // Check for workout time conflicts before creating new workout
        const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
          workoutStart,
          workoutEnd,
          workouts
        );

        if (hasTimeConflict && conflictingWorkouts.length > 0) {
          toast.error('Cannot create workout: time slot conflicts with existing workout');
          return;
        }

        // Create new workout and add customer
        const startTime = new Date(targetDateTime);
        const endTime = new Date(startTime);
        endTime.setHours(startTime.getHours() + 1); // 1-hour duration

        const newWorkout = await createWorkout({
          title: `Training Session`,
          description: '',
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          location: '',
          minParticipants: 3,
          maxParticipants: 5,
        });

        // Add customer to the new workout
        if (newWorkout) {
          const newParticipant = await addParticipant(newWorkout.id, { customerId: customer.id });
          if (newParticipant) {
            toast.success(`${customer.name} added to new workout`);
          }
        }
      }

      // Refresh the workout list to show updated data
      mutate();
    } catch (error) {
      console.error('Error handling customer drop:', error);
      toast.error('Failed to add customer to workout');
    }
  };

  return (
    <>
      <div className="flex-1 min-w-0">
        <EventCalendar
          events={visibleEvents}
          onEventCreate={handleWorkoutCreate}
          onEventUpdate={handleWorkoutUpdate}
          onEventDelete={handleWorkoutDeleteFromCalendar}
          onEventClick={handleWorkoutClick}
          initialView="week"
        />
      </div>

      {/* Workout Details Modal */}
      <WorkoutDetailsModal
        workoutId={selectedWorkoutId}
        open={workoutModalOpen}
        onOpenChange={setWorkoutModalOpen}
        onEdit={handleWorkoutEdit}
        onDelete={handleWorkoutDelete}
        onWorkoutUpdated={() => mutate()} // Pass callback to refresh workout list when workout is updated
        refreshTrigger={refreshTrigger}
      />

      {/* Workout Edit Modal */}
      <WorkoutEditModal
        workout={editWorkout}
        open={workoutEditModalOpen}
        onOpenChange={setWorkoutEditModalOpen}
        onSuccess={() => {
          mutate(); // Refresh workout list
          setEditWorkout(null);
          setRefreshTrigger((prev) => prev + 1); // Trigger refresh of workout details modal
        }}
      />
    </>
  );
}
