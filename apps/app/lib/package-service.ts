import { db, packages, customerPackages, customers } from '@workspace/auth/server';
import { eq, and, desc, asc, count, sum, sql } from 'drizzle-orm';
import type { CreatePackageInput, UpdatePackageInput, AssignPackageInput } from './validations';

/**
 * Creates a new package for a trainer
 */
export async function createPackage(trainerId: string, packageData: CreatePackageInput) {
  const [newPackage] = await db
    .insert(packages)
    .values({
      trainerId,
      name: packageData.name,
      description: packageData.description || null,
      sessionCount: packageData.sessionCount,
      price: packageData.price.toString(),
      isActive: packageData.isActive,
    })
    .returning();

  return newPackage;
}

/**
 * Updates an existing package
 */
export async function updatePackage(packageId: string, trainerId: string, packageData: UpdatePackageInput) {
  const [updatedPackage] = await db
    .update(packages)
    .set({
      ...(packageData.name && { name: packageData.name }),
      ...(packageData.description !== undefined && { description: packageData.description || null }),
      ...(packageData.sessionCount && { sessionCount: packageData.sessionCount }),
      ...(packageData.price && { price: packageData.price.toString() }),
      ...(packageData.isActive !== undefined && { isActive: packageData.isActive }),
      updatedAt: new Date(),
    })
    .where(and(eq(packages.id, packageId), eq(packages.trainerId, trainerId)))
    .returning();

  return updatedPackage;
}

/**
 * Deletes (deactivates) a package
 */
export async function deletePackage(packageId: string, trainerId: string) {
  // Check if package is assigned to any customers
  const [assignmentCount] = await db
    .select({ count: count() })
    .from(customerPackages)
    .where(eq(customerPackages.packageId, packageId));

  if (assignmentCount && assignmentCount.count > 0) {
    throw new Error('Cannot delete package that is assigned to customers. Deactivate it instead.');
  }

  const [deletedPackage] = await db
    .update(packages)
    .set({ isActive: false, updatedAt: new Date() })
    .where(and(eq(packages.id, packageId), eq(packages.trainerId, trainerId)))
    .returning();

  return deletedPackage;
}

/**
 * Gets all packages for a trainer
 */
export async function getPackagesByTrainer(trainerId: string, activeOnly: boolean = false) {
  const conditions = [eq(packages.trainerId, trainerId)];
  if (activeOnly) {
    conditions.push(eq(packages.isActive, true));
  }

  return await db
    .select()
    .from(packages)
    .where(and(...conditions))
    .orderBy(asc(packages.name));
}

/**
 * Gets a specific package by ID
 */
export async function getPackageById(packageId: string, trainerId: string) {
  const [package_] = await db
    .select()
    .from(packages)
    .where(and(eq(packages.id, packageId), eq(packages.trainerId, trainerId)));

  return package_;
}

/**
 * Assigns a package to a customer
 */
export async function assignPackageToCustomer(customerId: string, packageData: AssignPackageInput, trainerId: string) {
  return await db.transaction(async (tx) => {
    // Verify package exists and belongs to trainer
    const [package_] = await tx
      .select()
      .from(packages)
      .where(and(eq(packages.id, packageData.packageId), eq(packages.trainerId, trainerId)));

    if (!package_) {
      throw new Error('Package not found');
    }

    if (!package_.isActive) {
      throw new Error('Cannot assign inactive package');
    }

    // Verify customer belongs to trainer
    const [customer] = await tx
      .select()
      .from(customers)
      .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

    if (!customer) {
      throw new Error('Customer not found');
    }

    // Check if customer already has this package type
    const existingPackages = await tx
      .select()
      .from(customerPackages)
      .where(and(eq(customerPackages.customerId, customerId), eq(customerPackages.packageId, packageData.packageId)));

    let newCustomerPackage;

    if (existingPackages.length > 0) {
      // Combine with existing package of same type
      const existingPackage = existingPackages[0];

      if (!existingPackage) {
        throw new Error('Failed to retrieve existing package');
      }

      const newSessionsRemaining = existingPackage.sessionsRemaining + package_.sessionCount;

      [newCustomerPackage] = await tx
        .update(customerPackages)
        .set({
          sessionsRemaining: newSessionsRemaining,
          updatedAt: new Date(),
        })
        .where(eq(customerPackages.id, existingPackage.id))
        .returning();
    } else {
      // Create new package assignment
      [newCustomerPackage] = await tx
        .insert(customerPackages)
        .values({
          customerId,
          packageId: packageData.packageId,
          sessionsRemaining: package_.sessionCount,
        })
        .returning();
    }

    return newCustomerPackage;
  });
}

/**
 * Gets all packages assigned to a customer with package details
 */
export async function getCustomerPackages(customerId: string, trainerId: string) {
  return await db
    .select({
      id: customerPackages.id,
      customerId: customerPackages.customerId,
      packageId: customerPackages.packageId,
      packageName: packages.name,
      packageDescription: packages.description,
      originalSessionCount: packages.sessionCount,
      sessionsRemaining: customerPackages.sessionsRemaining,
      purchaseDate: customerPackages.purchaseDate,
      createdAt: customerPackages.createdAt,
      updatedAt: customerPackages.updatedAt,
    })
    .from(customerPackages)
    .innerJoin(packages, eq(customerPackages.packageId, packages.id))
    .innerJoin(customers, eq(customerPackages.customerId, customers.id))
    .where(and(eq(customerPackages.customerId, customerId), eq(customers.trainerId, trainerId)))
    .orderBy(asc(customerPackages.purchaseDate));
}

/**
 * Calculates total sessions remaining for a customer across all packages
 */
export async function calculateCustomerTotalSessions(customerId: string) {
  const [result] = await db
    .select({
      totalSessions: sum(customerPackages.sessionsRemaining),
    })
    .from(customerPackages)
    .where(eq(customerPackages.customerId, customerId));

  return Number(result?.totalSessions || 0);
}

/**
 * Deducts a session from customer packages using FIFO (First In, First Out)
 */
export async function deductSessionFromPackages(customerId: string) {
  return await db.transaction(async (tx) => {
    // Get customer packages ordered by purchase date (FIFO)
    const customerPackagesList = await tx
      .select()
      .from(customerPackages)
      .where(and(eq(customerPackages.customerId, customerId), sql`${customerPackages.sessionsRemaining} > 0`))
      .orderBy(asc(customerPackages.purchaseDate));

    if (customerPackagesList.length === 0) {
      throw new Error('No sessions available for deduction');
    }

    // Deduct from the oldest package
    const oldestPackage = customerPackagesList[0];

    if (!oldestPackage) {
      throw new Error('Failed to retrieve oldest package');
    }

    const newSessionsRemaining = oldestPackage.sessionsRemaining - 1;

    await tx
      .update(customerPackages)
      .set({
        sessionsRemaining: newSessionsRemaining,
        updatedAt: new Date(),
      })
      .where(eq(customerPackages.id, oldestPackage.id));

    return {
      packageId: oldestPackage.packageId,
      sessionsRemaining: newSessionsRemaining,
      deductedFrom: 'package',
    };
  });
}

/**
 * Refunds a session to customer packages (adds back to most recently deducted package)
 */
export async function refundSessionToPackages(customerId: string) {
  return await db.transaction(async (tx) => {
    // Get customer packages ordered by purchase date (most recent first for refund)
    const customerPackagesList = await tx
      .select()
      .from(customerPackages)
      .where(eq(customerPackages.customerId, customerId))
      .orderBy(desc(customerPackages.purchaseDate));

    if (customerPackagesList.length === 0) {
      throw new Error('No packages found for refund');
    }

    // Refund to the most recent package
    const mostRecentPackage = customerPackagesList[0];

    if (!mostRecentPackage) {
      throw new Error('Failed to retrieve most recent package');
    }

    const newSessionsRemaining = mostRecentPackage.sessionsRemaining + 1;

    await tx
      .update(customerPackages)
      .set({
        sessionsRemaining: newSessionsRemaining,
        updatedAt: new Date(),
      })
      .where(eq(customerPackages.id, mostRecentPackage.id));

    return {
      packageId: mostRecentPackage.packageId,
      sessionsRemaining: newSessionsRemaining,
      refundedTo: 'package',
    };
  });
}
