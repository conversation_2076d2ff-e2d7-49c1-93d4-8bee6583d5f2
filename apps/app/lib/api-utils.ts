import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth/server';
import { ZodError, ZodSchema } from 'zod';
import { type ErrorResponse } from './validations';

// Helper to get authenticated user from request
export async function getAuthenticatedUser(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return null;
    }

    return session.user;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

// Helper to validate request body with Zod schema
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; error: ErrorResponse }> {
  try {
    const body = await request.json();
    const validatedData = schema.parse(body);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          error: 'Validation Error',
          message: 'Invalid request data',
          details: error.flatten().fieldErrors,
        },
      };
    }
    return {
      success: false,
      error: {
        error: 'Parse Error',
        message: 'Invalid JSON in request body',
      },
    };
  }
}

// Helper to validate query parameters with Zod schema
export function validateQueryParams<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): { success: true; data: T } | { success: false; error: ErrorResponse } {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    const validatedData = schema.parse(params);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          error: 'Validation Error',
          message: 'Invalid query parameters',
          details: error.flatten().fieldErrors,
        },
      };
    }
    return {
      success: false,
      error: {
        error: 'Parse Error',
        message: 'Invalid query parameters',
      },
    };
  }
}

// Helper to create error responses
export function createErrorResponse(
  error: string,
  message: string,
  status: number = 400,
  details?: Record<string, any>
): NextResponse<ErrorResponse> {
  const errorResponse: ErrorResponse = {
    error,
    message,
    ...(details && { details }),
  };

  return NextResponse.json(errorResponse, { status });
}

// Helper to create success responses
export function createSuccessResponse<T>(data: T, status: number = 200): NextResponse<T> {
  return NextResponse.json(data, { status });
}

// Helper to handle API route errors
export function handleApiError(error: unknown): NextResponse<ErrorResponse> {
  console.error('API Error:', error);

  if (error instanceof Error) {
    return createErrorResponse('Internal Server Error', error.message, 500);
  }

  return createErrorResponse('Internal Server Error', 'An unexpected error occurred', 500);
}

// Middleware wrapper for API routes with authentication
export function withAuth<T extends any[]>(
  handler: (
    request: NextRequest,
    user: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const user = await getAuthenticatedUser(request);

      if (!user) {
        return createErrorResponse('Unauthorized', 'Authentication required', 401);
      }

      return await handler(request, user, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Helper to get trainer ID from authenticated user
export async function getTrainerIdFromUser(
  user: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>
): Promise<string | null> {
  // Find trainer by email since better-auth user.id is text but trainer.id is UUID
  const { db, trainers } = await import('@workspace/auth/server');
  const { eq } = await import('drizzle-orm');

  try {
    // First, try to find existing trainer
    const [existingTrainer] = await db.select({ id: trainers.id }).from(trainers).where(eq(trainers.email, user.email));

    if (existingTrainer) {
      return existingTrainer.id;
    }

    // If no trainer exists, create one
    const [newTrainer] = await db
      .insert(trainers)
      .values({
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified,
        image: user.image,
      })
      .returning({ id: trainers.id });

    if (!newTrainer) {
      throw new Error('Failed to create trainer');
    }

    return newTrainer.id;
  } catch (error) {
    console.error('Error finding/creating trainer:', error);
    return null;
  }
}
