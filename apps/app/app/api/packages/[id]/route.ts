import { NextRequest } from 'next/server';
import { updatePackageSchema, type PackageResponse } from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { getPackageById, updatePackage, deletePackage } from '@/lib/package-service';

// GET /api/packages/[id] - Get specific package
export const GET = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id: packageId } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const package_ = await getPackageById(packageId, trainerId);

    if (!package_) {
      return createErrorResponse('Not Found', 'Package not found', 404);
    }

    const response: PackageResponse = {
      id: package_.id,
      trainerId: package_.trainerId,
      name: package_.name,
      description: package_.description,
      sessionCount: package_.sessionCount,
      price: package_.price,
      isActive: package_.isActive,
      createdAt: package_.createdAt,
      updatedAt: package_.updatedAt,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// PUT /api/packages/[id] - Update package
export const PUT = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id: packageId } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, updatePackageSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const packageData = bodyValidation.data;

    const updatedPackage = await updatePackage(packageId, trainerId, packageData);

    if (!updatedPackage) {
      return createErrorResponse('Not Found', 'Package not found', 404);
    }

    const response: PackageResponse = {
      id: updatedPackage.id,
      trainerId: updatedPackage.trainerId,
      name: updatedPackage.name,
      description: updatedPackage.description,
      sessionCount: updatedPackage.sessionCount,
      price: updatedPackage.price,
      isActive: updatedPackage.isActive,
      createdAt: updatedPackage.createdAt,
      updatedAt: updatedPackage.updatedAt,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// DELETE /api/packages/[id] - Delete (deactivate) package
export const DELETE = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id: packageId } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const deletedPackage = await deletePackage(packageId, trainerId);

    if (!deletedPackage) {
      return createErrorResponse('Not Found', 'Package not found', 404);
    }

    const response: PackageResponse = {
      id: deletedPackage.id,
      trainerId: deletedPackage.trainerId,
      name: deletedPackage.name,
      description: deletedPackage.description,
      sessionCount: deletedPackage.sessionCount,
      price: deletedPackage.price,
      isActive: deletedPackage.isActive,
      createdAt: deletedPackage.createdAt,
      updatedAt: deletedPackage.updatedAt,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});
