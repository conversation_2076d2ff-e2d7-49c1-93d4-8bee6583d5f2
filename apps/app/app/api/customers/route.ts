import { NextRequest } from 'next/server';
import { db, customers } from '@workspace/auth/server';
import { eq, and, ilike, asc, desc, count } from 'drizzle-orm';
import {
  createCustomerSchema,
  customerQuerySchema,
  type PaginatedResponse,
  type CustomerResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { calculateCustomerTotalSessions } from '@/lib/package-service';

// GET /api/customers - List customers with search and pagination
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const queryValidation = validateQueryParams(request, customerQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { search, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build where conditions
    const whereConditions = [eq(customers.trainerId, trainerId)];

    if (search) {
      whereConditions.push(ilike(customers.name, `%${search}%`));
    }

    // Build order by - ensure we have a valid column
    const validSortBy = sortBy || 'name';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Map sortBy to actual columns
    const sortColumns = {
      name: customers.name,
      email: customers.email,
      createdAt: customers.createdAt,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || customers.name;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(customers)
      .where(and(...whereConditions));

    if (!totalResult) {
      throw new Error('Failed to fetch total count');
    }

    const total = totalResult.count;

    // Get customers with proper type safety
    const customerList = await db
      .select()
      .from(customers)
      .where(and(...whereConditions))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 20)
      .offset(offset || 0);

    // Ensure we have valid limit and offset values
    const validLimit = limit || 20;
    const validOffset = offset || 0;

    // Calculate total sessions for each customer
    const customersWithSessions = await Promise.all(
      customerList.map(async (customer) => {
        const totalSessions = await calculateCustomerTotalSessions(customer.id);
        return {
          id: customer.id,
          trainerId: customer.trainerId,
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
          totalSessions,
          parentName: customer.parentName,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt,
        };
      })
    );

    const response: PaginatedResponse<CustomerResponse> = {
      data: customersWithSessions,
      pagination: {
        total,
        limit: validLimit,
        offset: validOffset,
        hasMore: validOffset + validLimit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/customers - Create new customer
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, createCustomerSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const customerData = bodyValidation.data;

    // Create customer
    const [newCustomer] = await db
      .insert(customers)
      .values({
        trainerId,
        name: customerData.name,
        email: customerData.email || null,
        phone: customerData.phone || null,
        parentName: customerData.parentName || null,
      })
      .returning();

    if (!newCustomer) {
      throw new Error('Failed to create customer');
    }

    // Calculate total sessions from packages
    const totalSessions = await calculateCustomerTotalSessions(newCustomer.id);

    const response: CustomerResponse = {
      id: newCustomer.id,
      trainerId: newCustomer.trainerId,
      name: newCustomer.name,
      email: newCustomer.email,
      phone: newCustomer.phone,
      totalSessions,
      parentName: newCustomer.parentName,
      createdAt: newCustomer.createdAt,
      updatedAt: newCustomer.updatedAt,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
