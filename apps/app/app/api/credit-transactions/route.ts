import { NextRequest } from 'next/server';
import { db, creditTransactions, customers } from '@workspace/auth/server';
import { eq, and, desc, asc, sql } from 'drizzle-orm';
import { creditTransactionQuerySchema } from '@/lib/validations';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';

// GET /api/credit-transactions - Get credit transactions with filtering
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());

    const queryValidation = creditTransactionQuerySchema.safeParse(queryParams);
    if (!queryValidation.success) {
      return createErrorResponse(
        'Bad Request',
        'Invalid query parameters',
        400,
        queryValidation.error.flatten().fieldErrors
      );
    }

    const { customerId, type, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build query conditions
    const conditions = [
      // Only show transactions for customers belonging to this trainer
      eq(customers.trainerId, trainerId),
    ];

    if (customerId) {
      conditions.push(eq(creditTransactions.customerId, customerId));
    }

    if (type) {
      conditions.push(eq(creditTransactions.type, type));
    }

    // Get transactions with customer info
    const transactions = await db
      .select({
        id: creditTransactions.id,
        customerId: creditTransactions.customerId,
        customerName: customers.name,
        type: creditTransactions.type,
        amount: creditTransactions.amount,
        balanceBefore: creditTransactions.balanceBefore,
        balanceAfter: creditTransactions.balanceAfter,
        description: creditTransactions.description,
        relatedPurchaseId: creditTransactions.relatedPurchaseId,
        relatedWorkoutId: creditTransactions.relatedWorkoutId,
        relatedParticipantId: creditTransactions.relatedParticipantId,
        createdAt: creditTransactions.createdAt,
      })
      .from(creditTransactions)
      .innerJoin(customers, eq(creditTransactions.customerId, customers.id))
      .where(and(...conditions))
      .orderBy(sortOrder === 'desc' ? desc(creditTransactions[sortBy]) : asc(creditTransactions[sortBy]))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const countResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(creditTransactions)
      .innerJoin(customers, eq(creditTransactions.customerId, customers.id))
      .where(and(...conditions));

    const count = countResult[0]?.count || 0;

    const response = {
      data: transactions.map((transaction) => ({
        id: transaction.id,
        customerId: transaction.customerId,
        customerName: transaction.customerName,
        type: transaction.type,
        amount: transaction.amount,
        balanceBefore: transaction.balanceBefore,
        balanceAfter: transaction.balanceAfter,
        description: transaction.description,
        relatedPurchaseId: transaction.relatedPurchaseId,
        relatedWorkoutId: transaction.relatedWorkoutId,
        relatedParticipantId: transaction.relatedParticipantId,
        createdAt: transaction.createdAt,
      })),
      pagination: {
        total: count,
        limit,
        offset,
        hasMore: offset + limit < count,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});
