import type { Metadata } from 'next';
import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { CustomerManagement } from '@/components/customers/customer-management';

export const metadata: Metadata = {
  title: 'Customers - LooLooKids',
  description: 'Manage your customers and their workout credits',
};

export default function CustomersPage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="flex flex-1 flex-col gap-4 p-2 pt-0">
          <CustomerManagement />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
