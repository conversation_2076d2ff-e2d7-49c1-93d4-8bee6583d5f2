import type { Metada<PERSON> } from 'next';

export const metadata: Metadata = {
  title: 'LooLooKids',
};

import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import CalendarWithSidebar from '@/components/calendar-with-sidebar';
import { CustomerSidebar } from '@/components/customers/customer-sidebar';
import { CalendarDndWrapper } from '@/components/calendar-dnd-wrapper';

export default function Page() {
  return (
    <SidebarProvider>
      <CalendarDndWrapper>
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-1 flex-col gap-4 p-2 pt-0">
            <CalendarWithSidebar />
          </div>
        </SidebarInset>
        <CustomerSidebar />
      </CalendarDndWrapper>
    </SidebarProvider>
  );
}
