// import { NextRequest, NextResponse } from "next/server";

// export async function middleware(request: NextRequest) {
//   const pathname = request.nextUrl.pathname;

//   // Skip auth check for auth routes and public assets
//   if (
//     pathname.startsWith("/api/auth") ||
//     pathname.startsWith("/login") ||
//     pathname.startsWith("/register") ||
//     pathname.startsWith("/_next") ||
//     pathname.startsWith("/favicon")
//   ) {
//     return NextResponse.next();
//   }

//   // Check for session token in cookies
//   const sessionToken = request.cookies.get("better-auth.session-token");

//   if (!sessionToken) {
//     // Redirect to login if not authenticated
//     const loginUrl = new URL("/login", request.url);
//     return NextResponse.redirect(loginUrl);
//   }

//   return NextResponse.next();
// }

// export const config = {
//   matcher: [
//     /*
//      * Match all request paths except for the ones starting with:
//      * - api (API routes)
//      * - _next/static (static files)
//      * - _next/image (image optimization files)
//      * - favicon.ico (favicon file)
//      */
//     "/((?!api|_next/static|_next/image|favicon.ico).*)",
//   ],
// };

import { getSessionCookie } from '@workspace/auth/server';
import { type NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  // Check cookie for optimistic redirects for protected routes
  // Use getSession in your RSC to protect a route via SSR or useAuthenticate client side
  const sessionCookie = getSessionCookie(request);

  if (!sessionCookie) {
    const redirectTo = request.nextUrl.pathname + request.nextUrl.search;
    return NextResponse.redirect(new URL(`/auth/sign-in?redirectTo=${redirectTo}`, request.url));
  }

  return NextResponse.next();
}

export const config = {
  // Protected routes
  matcher: ['/', '/customers', '/admin'],
};
