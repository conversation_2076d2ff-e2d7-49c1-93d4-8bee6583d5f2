ALTER TABLE "credit_transactions" DROP CONSTRAINT "credit_transactions_customer_id_customers_id_fk";
--> statement-breakpoint
ALTER TABLE "customer_purchases" DROP CONSTRAINT "customer_purchases_customer_id_customers_id_fk";
--> statement-breakpoint
ALTER TABLE "workout_participants" DROP CONSTRAINT "workout_participants_customer_id_customers_id_fk";
--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD CONSTRAINT "credit_transactions_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_purchases" ADD CONSTRAINT "customer_purchases_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "workout_participants" ADD CONSTRAINT "workout_participants_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE cascade ON UPDATE no action;