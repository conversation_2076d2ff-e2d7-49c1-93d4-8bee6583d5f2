{"id": "d842b9a2-d31c-4ffe-b8d1-54812550e051", "prevId": "7f77f191-7154-4ea8-a3e2-00c43fde6611", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "balance_before": {"name": "balance_before", "type": "integer", "primaryKey": false, "notNull": true}, "balance_after": {"name": "balance_after", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "related_purchase_id": {"name": "related_purchase_id", "type": "uuid", "primaryKey": false, "notNull": false}, "related_workout_id": {"name": "related_workout_id", "type": "uuid", "primaryKey": false, "notNull": false}, "related_participant_id": {"name": "related_participant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"credit_transactions_customer_id_customers_id_fk": {"name": "credit_transactions_customer_id_customers_id_fk", "tableFrom": "credit_transactions", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "credit_transactions_related_purchase_id_customer_purchases_id_fk": {"name": "credit_transactions_related_purchase_id_customer_purchases_id_fk", "tableFrom": "credit_transactions", "tableTo": "customer_purchases", "columnsFrom": ["related_purchase_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "credit_transactions_related_workout_id_workouts_id_fk": {"name": "credit_transactions_related_workout_id_workouts_id_fk", "tableFrom": "credit_transactions", "tableTo": "workouts", "columnsFrom": ["related_workout_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "credit_transactions_related_participant_id_workout_participants_id_fk": {"name": "credit_transactions_related_participant_id_workout_participants_id_fk", "tableFrom": "credit_transactions", "tableTo": "workout_participants", "columnsFrom": ["related_participant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_packages": {"name": "customer_packages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "package_id": {"name": "package_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sessions_remaining": {"name": "sessions_remaining", "type": "integer", "primaryKey": false, "notNull": true}, "purchase_date": {"name": "purchase_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"customer_packages_customer_id_customers_id_fk": {"name": "customer_packages_customer_id_customers_id_fk", "tableFrom": "customer_packages", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_packages_package_id_packages_id_fk": {"name": "customer_packages_package_id_packages_id_fk", "tableFrom": "customer_packages", "tableTo": "packages", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_purchases": {"name": "customer_purchases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "package_id": {"name": "package_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sessions_purchased": {"name": "sessions_purchased", "type": "integer", "primaryKey": false, "notNull": true}, "amount_paid": {"name": "amount_paid", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "purchase_date": {"name": "purchase_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'completed'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"customer_purchases_customer_id_customers_id_fk": {"name": "customer_purchases_customer_id_customers_id_fk", "tableFrom": "customer_purchases", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_purchases_package_id_packages_id_fk": {"name": "customer_purchases_package_id_packages_id_fk", "tableFrom": "customer_purchases", "tableTo": "packages", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "trainer_id": {"name": "trainer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "parent_name": {"name": "parent_name", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"customers_trainer_id_trainers_id_fk": {"name": "customers_trainer_id_trainers_id_fk", "tableFrom": "customers", "tableTo": "trainers", "columnsFrom": ["trainer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.packages": {"name": "packages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "trainer_id": {"name": "trainer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "session_count": {"name": "session_count", "type": "integer", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"packages_trainer_id_trainers_id_fk": {"name": "packages_trainer_id_trainers_id_fk", "tableFrom": "packages", "tableTo": "trainers", "columnsFrom": ["trainer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.trainers": {"name": "trainers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"trainers_email_unique": {"name": "trainers_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "default": "'trainer'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workout_participants": {"name": "workout_participants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workout_id": {"name": "workout_id", "type": "uuid", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'enrolled'"}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "confirmed_at": {"name": "confirmed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "credit_deducted": {"name": "credit_deducted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"workout_participants_workout_id_workouts_id_fk": {"name": "workout_participants_workout_id_workouts_id_fk", "tableFrom": "workout_participants", "tableTo": "workouts", "columnsFrom": ["workout_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workout_participants_customer_id_customers_id_fk": {"name": "workout_participants_customer_id_customers_id_fk", "tableFrom": "workout_participants", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workouts": {"name": "workouts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "trainer_id": {"name": "trainer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "min_participants": {"name": "min_participants", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workouts_trainer_id_trainers_id_fk": {"name": "workouts_trainer_id_trainers_id_fk", "tableFrom": "workouts", "tableTo": "trainers", "columnsFrom": ["trainer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}