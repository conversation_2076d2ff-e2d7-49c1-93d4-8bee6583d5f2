-- Migration: Remove customer_purchases table and update credit_transactions
-- This migration removes the redundant customer_purchases table since all purchases
-- are now handled through the package system (customer_packages table)

-- First, update credit_transactions to use relatedPackageId instead of relatedPurchaseId
ALTER TABLE "credit_transactions" 
ADD COLUMN "related_package_id" uuid REFERENCES "customer_packages"("id");

-- Drop the old foreign key constraint and column
ALTER TABLE "credit_transactions" 
DROP COLUMN "related_purchase_id";

-- Drop the customer_purchases table
DROP TABLE IF EXISTS "customer_purchases";
